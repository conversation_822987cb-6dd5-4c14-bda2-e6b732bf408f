import {
  CallH<PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { BetterStackService } from 'src/logger/better-stack.service';

@Injectable()
export class LoggingHttp implements NestInterceptor {
  constructor(private readonly logger: BetterStackService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<unknown> {
    const now = Date.now();
    const isHttp = context.getType() === 'http';
    return next.handle().pipe(
      tap(async () => {
        if (isHttp) {
          const res = context.switchToHttp().getResponse<Response>();
          const req = context.switchToHttp().getRequest<Request>();
          const elapsedTime = Date.now() - now;
          const user = req.header('user');
          const log = {
            body: req.method === 'GET' ? 'hidden' : (req.body as object),
            elapsedTimeMs: elapsedTime,
            method: req.method,
            params: req.params as object,
            path: req.path,
            query: req.query as object,
            status: res.statusCode,
            user: user ? (JSON.parse(user) as object) : {},
          };
          await this.logger.log(req.path, log);
        }
      }),
    );
  }
}
