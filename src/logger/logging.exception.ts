import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { BetterStackService } from 'src/logger/better-stack.service';

@Catch()
export class LoggingException implements ExceptionFilter {
  constructor(private readonly logger: BetterStackService) {}

  async catch(exception: unknown, host: ArgumentsHost): Promise<void> {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();

    let error: object = {
      message: (exception?.toString() as string) ?? 'Unknown',
    };
    if (exception instanceof Error) {
      error = {
        message: exception.message,
        stack: exception.stack,
      };
    }

    const status =
      exception instanceof HttpException ? exception.getStatus() : 500;
    const request = response.req as Request;
    const logData = {
      body: response.req.body as object,
      error: exception as object,
      operation: request.method,
      path: request.originalUrl,
    };
    if (status < 500) {
      await this.logger.warn(request.originalUrl, logData);
      response.status(status).json(exception);
    } else {
      await this.logger.error(request.originalUrl, logData);
      response.status(status).json({
        error: error,
        message: 'Internal Server Error',
      });
    }
  }
}
