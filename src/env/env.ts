import { z } from "zod";

const schema = z.object({
  NODE_ENV: z.enum(['dev', 'prod', 'local']).default('dev'),
  PORT: z.coerce.number().default(3000),
  DB_URL: z.string().nonempty(),
  BETTER_STACK_URL: z.string().nonempty(),
  BETTER_STACK_TOKEN: z.string().nonempty(),
  SENTRY_DSN: z.string().nonempty(),
  AWS_REGION: z.string().nonempty(),
  SHOPIFY_SECRET_KEY: z.string().nonempty(),
  QUEUE_WEBHOOK_URL: z.string().nonempty(),
  isLocal: z.boolean().default(false),
  isProd: z.boolean().default(false),
  isDev: z.boolean().default(false),
});

let env = schema.parse(process.env);
env.isLocal = env.NODE_ENV === 'local';
env.isProd = env.NODE_ENV === 'prod';
env.isDev = env.NODE_ENV === 'dev';

export default env;