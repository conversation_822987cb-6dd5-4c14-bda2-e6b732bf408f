type ShopifyHeaders = {
  webhookId: string;
  apiVersion: string;
  domain: string;
  topic: string;
  hmac: string;
  headers: Headers;
};

export function extractShopifyHeaders(headers: Headers): ShopifyHeaders {
  const getHeader = (headerName: string): string => {
    const lowerHeader = headerName.toLowerCase();
    const key = Object.keys(headers).find(
      (k) => k.toLowerCase() === lowerHeader,
    );
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    return key ? String(headers[key]) : '';
  };

  return {
    webhookId: getHeader('X-Shopify-Webhook-Id'),
    apiVersion: getHeader('X-Shopify-Api-Version'),
    domain: getHeader('X-Shopify-Shop-Domain'),
    topic: getHeader('X-Shopify-Topic'),
    hmac: getHeader('X-Shopify-Hmac-Sha256'),
    headers: { ...headers },
  };
}
