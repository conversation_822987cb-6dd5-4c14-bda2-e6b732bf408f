import * as Sentry from '@sentry/nestjs';

export function catchErrors(
  service: string,
  method: string,
  error: any,
  extra?: Record<string, any>,
): void {
  console.error('catchErrors', error);

  Sentry.withScope(function (scope: any) {
    scope.setTransactionName(`${service} service error`);
    scope.setFingerprint([method, String(error?.response?.status || 500)]);
    scope.setTag('method', method);
    Sentry.captureException(error?.toString(), {
      level: 'error',
      extra: {
        error: {
          stack: error.toString(),
          ...extra,
        },
      },
    });
  });
}
